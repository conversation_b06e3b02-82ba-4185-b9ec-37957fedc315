<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>South Safari - Partner with SS and Stay Relevant</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
            --light-bg: #F5F5F5;
            --text-dark: #333333;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--text-dark);
        }

        /* Header Styles */
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color) !important;
        }

        .nav-link {
            color: var(--text-dark) !important;
            font-weight: 500;
            margin: 0 1rem;
            transition: all 0.3s;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background: #00965F;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #00B074 0%, #00965F 100%);
            padding: 100px 0;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            right: -200px;
            top: -200px;
            width: 600px;
            height: 600px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .search-box {
            background: white;
            border-radius: 50px;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            max-width: 600px;
        }

        .search-box input {
            border: none;
            outline: none;
            flex: 1;
            padding: 0 1rem;
            font-size: 1.1rem;
        }

        .search-box button {
            background: var(--primary-color);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
        }

        /* Stats Section */
        .stats-section {
            background: var(--light-bg);
            padding: 60px 0;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 1.1rem;
            color: #666;
            margin-top: 0.5rem;
        }

        /* Project Cards */
        .project-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: all 0.3s;
            height: 100%;
            border: 2px solid transparent;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .project-category {
            background: #E8F5F0;
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--secondary-color);
        }

        .project-description {
            color: #666;
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }

        .tech-stack {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .tech-badge {
            background: #F0F0F0;
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            font-size: 0.8rem;
            color: #555;
        }

        /* How It Works */
        .how-it-works {
            padding: 80px 0;
        }

        .step-card {
            text-align: center;
            padding: 2rem;
            position: relative;
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 auto 1.5rem;
        }

        .step-card h4 {
            font-weight: 700;
            margin-bottom: 1rem;
        }

        /* Features Section */
        .features-section {
            background: var(--light-bg);
            padding: 80px 0;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            height: 100%;
            transition: all 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: #E8F5F0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: var(--primary-color);
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, #00B074 0%, #00965F 100%);
            padding: 80px 0;
            color: white;
            text-align: center;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .cta-subtitle {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        /* Footer */
        .footer {
            background: var(--secondary-color);
            color: white;
            padding: 60px 0 30px;
        }

        .footer h5 {
            font-weight: 700;
            margin-bottom: 1.5rem;
        }

        .footer-link {
            color: #ccc;
            text-decoration: none;
            display: block;
            margin-bottom: 0.75rem;
            transition: all 0.3s;
        }

        .footer-link:hover {
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .social-icons {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            transition: all 0.3s;
        }

        .social-icon:hover {
            background: var(--primary-color);
            transform: translateY(-3px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
            
            .search-box {
                flex-direction: column;
                padding: 0.5rem;
            }
            
            .search-box input {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-handshake"></i> South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#projects">Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#how-it-works">How It Works</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary">Sign In</button>
                    <button class="btn btn-primary">Become a Partner</button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="hero-title">Find the Best Partnership Opportunities</h1>
                    <p class="hero-subtitle">Partner with SS and Stay Relevant</p>
                    <p class="mb-4">Connect with Southern Africa's fastest-growing markets through sustainable technology partnerships. Transform your one-time sales into recurring revenue streams.</p>
                    <div class="search-box">
                        <input type="text" placeholder="Search partnership opportunities...">
                        <button><i class="fas fa-search me-2"></i> Search</button>
                    </div>
                    <div class="mt-4">
                        <button class="btn btn-light btn-lg me-3">
                            <i class="fas fa-briefcase me-2"></i> Find Partnerships
                        </button>
                        <button class="btn btn-outline-light btn-lg">
                            <i class="fas fa-rocket me-2"></i> Become a Partner
                        </button>
                    </div>
                </div>
                <div class="col-lg-6">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 500 400'%3E%3Crect x='50' y='50' width='400' height='300' rx='20' fill='rgba(255,255,255,0.1)'/%3E%3Ccircle cx='150' cy='150' r='40' fill='rgba(255,255,255,0.2)'/%3E%3Ccircle cx='350' cy='150' r='40' fill='rgba(255,255,255,0.2)'/%3E%3Cpath d='M 150 250 Q 250 300 350 250' stroke='rgba(255,255,255,0.3)' stroke-width='3' fill='none'/%3E%3C/svg%3E" alt="Partnership illustration" class="img-fluid">
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number" data-count="25">0</div>
                        <div class="stat-label">Active Partnerships</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number" data-count="150">0</div>
                        <div class="stat-label">Developers Onboarded</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number" data-count="50">0</div>
                        <div class="stat-label">Projects Launched</div>
                    </div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-card">
                        <div class="stat-number">R<span data-count="2.5">0</span>M</div>
                        <div class="stat-label">Revenue Generated</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Projects -->
    <section class="py-5" id="projects">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold mb-3">Featured Partnership Opportunities</h2>
                <p class="text-muted">Discover projects that match your expertise and grow your revenue</p>
            </div>
            
            <div class="row g-4">
                <!-- Project Card 1 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">Food Delivery</span>
                        <h4 class="project-title">Township Food Delivery Platform</h4>
                        <p class="project-description">Connect local restaurants with customers in underserved areas. Similar to Uber Eats but for townships.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">PHP</span>
                            <span class="tech-badge">MySQL</span>
                            <span class="tech-badge">Flutter</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Revenue Share: 50/50</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Project Card 2 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">Service Booking</span>
                        <h4 class="project-title">Salon & Spa Booking System</h4>
                        <p class="project-description">Digital booking platform for beauty services with SMS notifications and payment integration.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">Laravel</span>
                            <span class="tech-badge">Vue.js</span>
                            <span class="tech-badge">API</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Equity Partnership</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Project Card 3 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">FinTech</span>
                        <h4 class="project-title">Stokvel Management Platform</h4>
                        <p class="project-description">Digital solution for traditional savings groups with automated contributions and payouts.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">Node.js</span>
                            <span class="tech-badge">React</span>
                            <span class="tech-badge">MongoDB</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Profit Sharing</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Project Card 4 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">Transportation</span>
                        <h4 class="project-title">School Transport Tracker</h4>
                        <p class="project-description">Real-time tracking system for school buses with parent notifications and route optimization.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">Python</span>
                            <span class="tech-badge">Django</span>
                            <span class="tech-badge">React Native</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Revenue Share: 60/40</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Project Card 5 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">Healthcare</span>
                        <h4 class="project-title">Clinic Appointment System</h4>
                        <p class="project-description">Streamline patient bookings for local clinics with queue management and reminders.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">PHP</span>
                            <span class="tech-badge">Bootstrap</span>
                            <span class="tech-badge">MySQL</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Hybrid Model</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Project Card 6 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">E-Commerce</span>
                        <h4 class="project-title">Local Artisan Marketplace</h4>
                        <p class="project-description">Connect traditional crafters with modern customers through digital marketplace.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">WooCommerce</span>
                            <span class="tech-badge">WordPress</span>
                            <span class="tech-badge">PayGate</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Revenue Share: 55/45</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Project Card 7 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">Agriculture</span>
                        <h4 class="project-title">Farm Supply Chain Manager</h4>
                        <p class="project-description">Digital platform connecting small farmers with buyers and tracking produce from farm to market.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">Java</span>
                            <span class="tech-badge">Spring Boot</span>
                            <span class="tech-badge">PostgreSQL</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Equity Partnership</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>

                <!-- Project Card 8 -->
                <div class="col-lg-3 col-md-6">
                    <div class="project-card">
                        <span class="project-category">Services</span>
                        <h4 class="project-title">Home Services Platform</h4>
                        <p class="project-description">Connect plumbers, electricians, and handymen with customers needing home repairs.</p>
                        <div class="tech-stack">
                            <span class="tech-badge">Ruby on Rails</span>
                            <span class="tech-badge">Angular</span>
                            <span class="tech-badge">Redis</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold">Service Model</span>
                            <button class="btn btn-primary btn-sm">Apply Now</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5">
                <button class="btn btn-primary btn-lg">
                    <i class="fas fa-th me-2"></i> View All Projects
                </button>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section class="how-it-works bg-light" id="how-it-works">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold mb-3">How South Safari Works</h2>
                <p class="text-muted">Simple steps to transform your skills into sustainable revenue</p>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <h4>Browse Opportunities</h4>
                        <p class="text-muted">Explore partnership projects that match your expertise and technology stack</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <h4>Submit Application</h4>
                        <p class="text-muted">Apply with your portfolio and proposed approach to the project</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <h4>Partnership Agreement</h4>
                        <p class="text-muted">Negotiate terms and sign partnership agreement for long-term collaboration</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="step-card">
                        <div class="step-number">4</div>
                        <h4>Launch & Earn</h4>
                        <p class="text-muted">Deploy your solution and earn recurring revenue from real customers</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold mb-3">Why Partner with South Safari</h2>
                <p class="text-muted">Benefits that make us the preferred choice for developers worldwide</p>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4>Long-term Revenue</h4>
                        <p class="text-muted">Transform one-time sales into sustainable monthly recurring revenue streams</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4>Local Support</h4>
                        <p class="text-muted">We handle all on-ground operations, customer support, and market compliance</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-globe-africa"></i>
                        </div>
                        <h4>Market Access</h4>
                        <p class="text-muted">Tap into Southern Africa's fastest-growing digital economy with local expertise</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h4>Fair Terms</h4>
                        <p class="text-muted">Transparent partnership agreements with flexible revenue sharing models</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h4>Growth Potential</h4>
                        <p class="text-muted">Scale across SADC region as we expand to new markets together</p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h4>AI-Ready Future</h4>
                        <p class="text-muted">Stay relevant in the AI era through strategic product positioning</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <h2 class="cta-title">Ready to Transform Your Future?</h2>
            <p class="cta-subtitle">Join South Safari today and turn your skills into sustainable revenue</p>
            <button class="btn btn-light btn-lg me-3">
                <i class="fas fa-user-plus me-2"></i> Become a Partner
            </button>
            <button class="btn btn-outline-light btn-lg">
                <i class="fas fa-info-circle me-2"></i> Learn More
            </button>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><i class="fas fa-handshake me-2"></i> South Safari</h5>
                    <p class="text-muted">Partner with SS and Stay Relevant. Connecting talented developers with Southern African opportunities.</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-icon"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Quick Links</h5>
                    <a href="#" class="footer-link">Home</a>
                    <a href="#" class="footer-link">Projects</a>
                    <a href="#" class="footer-link">About Us</a>
                    <a href="#" class="footer-link">Contact</a>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>For Developers</h5>
                    <a href="#" class="footer-link">How It Works</a>
                    <a href="#" class="footer-link">Partnership Models</a>
                    <a href="#" class="footer-link">Success Stories</a>
                    <a href="#" class="footer-link">FAQs</a>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Legal</h5>
                    <a href="#" class="footer-link">Terms of Service</a>
                    <a href="#" class="footer-link">Privacy Policy</a>
                    <a href="#" class="footer-link">Partnership Agreement</a>
                    <a href="#" class="footer-link">POPIA Compliance</a>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Contact Info</h5>
                    <p class="text-muted mb-2">
                        <i class="fas fa-envelope me-2"></i> <EMAIL>
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-phone me-2"></i> +27 67 975 7128
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt me-2"></i> Johannesburg, South Africa
                    </p>
                </div>
            </div>
            
            <hr class="my-4 opacity-25">
            
            <div class="text-center">
                <p class="text-muted mb-0">&copy; 2024 South Safari. All rights reserved. Built with integrity and purpose.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Counter animation for stats
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('[data-count]');
            
            counters.forEach(counter => {
                const target = parseFloat(counter.getAttribute('data-count'));
                const duration = 2000;
                const increment = target / (duration / 16);
                let current = 0;
                
                const updateCounter = () => {
                    current += increment;
                    if (current < target) {
                        counter.textContent = Math.floor(current);
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target % 1 === 0 ? target : target.toFixed(1);
                    }
                };
                
                // Start animation when element is in viewport
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            updateCounter();
                            observer.unobserve(entry.target);
                        }
                    });
                });
                
                observer.observe(counter);
            });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>