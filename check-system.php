<?php
/**
 * South Safari Partnership Platform - System Check
 * Quick diagnostic page to check if everything is working
 */

// Allow direct access for this diagnostic script
define('SS_DIRECT_ACCESS', true);

// Buffer output to avoid header issues
ob_start();

$output = "<h1>South Safari System Check</h1>";

// Check if config can be loaded
try {
    require_once 'includes/config.php';
    $output .= "<p>✅ Configuration loaded successfully</p>";
} catch (Exception $e) {
    $output .= "<p>❌ Configuration failed: " . $e->getMessage() . "</p>";
    echo $output;
    exit;
}

// Check database connection
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    $output .= "<p>✅ Database connection successful</p>";

    // Check if tables exist
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    if (count($tables) > 0) {
        $output .= "<p>✅ Database has " . count($tables) . " tables</p>";
    } else {
        $output .= "<p>⚠️ Database exists but has no tables. <a href='setup-database.php'>Run database setup</a></p>";
    }

} catch (PDOException $e) {
    $output .= "<p>❌ Database connection failed: " . $e->getMessage() . "</p>";
    $output .= "<p><strong>Solutions:</strong></p>";
    $output .= "<ul>";
    $output .= "<li><a href='setup-database.php'>Run database setup script</a></li>";
    $output .= "<li>Check if XAMPP MySQL is running</li>";
    $output .= "<li>Verify database name: " . DB_NAME . "</li>";
    $output .= "</ul>";
}

// Check if init.php can be loaded
try {
    require_once 'includes/init.php';
    $output .= "<p>✅ Initialization successful</p>";
} catch (Exception $e) {
    $output .= "<p>❌ Initialization failed: " . $e->getMessage() . "</p>";
}

// Test key functions
if (function_exists('getPartnershipOpportunities')) {
    try {
        $opportunities = getPartnershipOpportunities(1);
        $output .= "<p>✅ Partnership functions working (found " . count($opportunities) . " opportunities)</p>";
    } catch (Exception $e) {
        $output .= "<p>⚠️ Partnership functions error: " . $e->getMessage() . "</p>";
    }
} else {
    $output .= "<p>❌ Partnership functions not available</p>";
}

$output .= "<h2>Quick Actions</h2>";
$output .= "<ul>";
$output .= "<li><a href='setup-database.php'>Setup Database</a></li>";
$output .= "<li><a href='index.php'>Test Homepage</a></li>";
$output .= "<li><a href='register.php'>Test Registration</a></li>";
$output .= "<li><a href='admin/'>Test Admin Panel</a></li>";
$output .= "</ul>";

// Output everything at once
ob_end_clean();
echo $output;
?>
