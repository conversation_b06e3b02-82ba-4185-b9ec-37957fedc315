<?php
// Simple database test
define('SS_DIRECT_ACCESS', true);

$dbHost = 'localhost';
$dbName = 'south_safari_partnerships';
$dbUser = 'root';
$dbPass = '';

try {
    // First connect without database to see all databases
    $dsn = "mysql:host={$dbHost};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbUser, $dbPass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);

    echo "MySQL connection successful\n";

    // Show all databases
    $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    echo "Available databases:\n";
    foreach ($databases as $db) {
        echo "- {$db}\n";
    }

    // Now connect to our specific database
    $dsn = "mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbUser, $dbPass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);

    echo "\nConnected to database: {$dbName}\n";
    
    // Check tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables found: " . count($tables) . "\n";
    foreach ($tables as $table) {
        echo "- {$table}\n";
    }
    
    // Check partnership opportunities
    $count = $pdo->query("SELECT COUNT(*) FROM partnership_opportunities")->fetchColumn();
    echo "\nPartnership opportunities: {$count}\n";
    
    if ($count > 0) {
        $opportunities = $pdo->query("SELECT id, title, status FROM partnership_opportunities LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($opportunities as $opp) {
            echo "- ID: {$opp['id']}, Title: {$opp['title']}, Status: {$opp['status']}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
