<?php
/**
 * South Safari Partnership Platform - Installation Script
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

// Check if already installed
if (file_exists('includes/config.php') && file_exists('.installed')) {
    die('South Safari Partnership Platform is already installed. Delete the .installed file to reinstall.');
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = false;

// Step 1: Database Configuration
if ($step == 1 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $dbHost = trim($_POST['db_host'] ?? '');
    $dbName = trim($_POST['db_name'] ?? '');
    $dbUser = trim($_POST['db_user'] ?? '');
    $dbPass = $_POST['db_pass'] ?? '';
    
    if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
        $errors[] = 'Please fill in all required database fields.';
    } else {
        // Test database connection
        try {
            $dsn = "mysql:host={$dbHost};charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `{$dbName}`");
            
            // Store database config in session for next step
            session_start();
            $_SESSION['install_db'] = [
                'host' => $dbHost,
                'name' => $dbName,
                'user' => $dbUser,
                'pass' => $dbPass
            ];
            
            header('Location: install.php?step=2');
            exit;
            
        } catch (PDOException $e) {
            $errors[] = 'Database connection failed: ' . $e->getMessage();
        }
    }
}

// Step 2: Create Tables and Admin User
if ($step == 2) {
    session_start();
    if (!isset($_SESSION['install_db'])) {
        header('Location: install.php?step=1');
        exit;
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $adminEmail = trim($_POST['admin_email'] ?? '');
        $adminPassword = $_POST['admin_password'] ?? '';
        $adminName = trim($_POST['admin_name'] ?? '');
        $siteUrl = trim($_POST['site_url'] ?? '');
        
        if (empty($adminEmail) || empty($adminPassword) || empty($adminName) || empty($siteUrl)) {
            $errors[] = 'Please fill in all required fields.';
        } elseif (!filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Please enter a valid email address.';
        } elseif (strlen($adminPassword) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        } else {
            try {
                $dbConfig = $_SESSION['install_db'];
                $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset=utf8mb4";
                $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                ]);
                
                // Read and execute schema
                $schema = file_get_contents('database/south_safari_schema.sql');
                $statements = explode(';', $schema);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                // Read and execute sample data
                if (file_exists('database/sample_data.sql')) {
                    $sampleData = file_get_contents('database/sample_data.sql');
                    $statements = explode(';', $sampleData);
                    
                    foreach ($statements as $statement) {
                        $statement = trim($statement);
                        if (!empty($statement)) {
                            $pdo->exec($statement);
                        }
                    }
                }
                
                // Create admin user
                $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (email, password_hash, full_name, role, status, email_verified) 
                    VALUES (?, ?, ?, 'admin', 'active', 1)
                    ON DUPLICATE KEY UPDATE 
                    password_hash = VALUES(password_hash), 
                    full_name = VALUES(full_name)
                ");
                $stmt->execute([$adminEmail, $passwordHash, $adminName]);
                
                // Update configuration file
                $configContent = file_get_contents('includes/config.php');
                $configContent = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '{$dbConfig['host']}');", $configContent);
                $configContent = str_replace("define('DB_NAME', 'south_safari_partnerships');", "define('DB_NAME', '{$dbConfig['name']}');", $configContent);
                $configContent = str_replace("define('DB_USER', 'root');", "define('DB_USER', '{$dbConfig['user']}');", $configContent);
                $configContent = str_replace("define('DB_PASS', '');", "define('DB_PASS', '{$dbConfig['pass']}');", $configContent);
                $configContent = str_replace("define('BASE_URL', 'http://localhost/south-safari-v3/');", "define('BASE_URL', '{$siteUrl}');", $configContent);
                
                // Generate secure keys
                $secretKey = bin2hex(random_bytes(32));
                $salt = bin2hex(random_bytes(32));
                $configContent = str_replace("define('SS_SECRET_KEY', 'your-secret-key-here-change-in-production');", "define('SS_SECRET_KEY', '{$secretKey}');", $configContent);
                $configContent = str_replace("define('SS_SALT', 'your-salt-here-change-in-production');", "define('SS_SALT', '{$salt}');", $configContent);
                
                file_put_contents('includes/config.php', $configContent);
                
                // Create installation marker
                file_put_contents('.installed', date('Y-m-d H:i:s'));
                
                // Clear session
                unset($_SESSION['install_db']);
                
                $success = true;
                
            } catch (Exception $e) {
                $errors[] = 'Installation failed: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install South Safari Partnership Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .install-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }
        
        .install-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 1rem;
            font-weight: bold;
        }
        
        .step.active {
            background: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 176, 116, 0.25);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #008c5a;
            border-color: #008c5a;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h1 class="h3 mb-2">
                    <i class="fas fa-handshake me-2"></i>
                    South Safari Partnership Platform
                </h1>
                <p class="mb-0">Installation Wizard</p>
            </div>
            
            <div class="install-body">
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending'; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending'; ?>">2</div>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>Installation Complete!</h5>
                        <p class="mb-3">South Safari Partnership Platform has been successfully installed.</p>
                        <div class="d-grid gap-2">
                            <a href="admin/" class="btn btn-primary">
                                <i class="fas fa-user-shield me-2"></i>Access Admin Panel
                            </a>
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-home me-2"></i>View Homepage
                            </a>
                        </div>
                    </div>
                <?php elseif ($step == 1): ?>
                    <!-- Step 1: Database Configuration -->
                    <h4 class="mb-3">Step 1: Database Configuration</h4>
                    <p class="text-muted mb-4">Enter your database connection details.</p>

                    <form method="POST">
                        <div class="mb-3">
                            <label for="db_host" class="form-label">Database Host</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" 
                                   value="<?php echo htmlspecialchars($_POST['db_host'] ?? 'localhost'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="db_name" class="form-label">Database Name</label>
                            <input type="text" class="form-control" id="db_name" name="db_name"
                                   value="<?php echo htmlspecialchars($_POST['db_name'] ?? 'south_safari'); ?>" required>
                            <div class="form-text">Database will be created if it doesn't exist</div>
                        </div>

                        <div class="mb-3">
                            <label for="db_user" class="form-label">Database Username</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" 
                                   value="<?php echo htmlspecialchars($_POST['db_user'] ?? 'root'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="db_pass" class="form-label">Database Password</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass">
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-arrow-right me-2"></i>Test Connection & Continue
                        </button>
                    </form>

                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Admin User & Site Configuration -->
                    <h4 class="mb-3">Step 2: Admin User & Site Configuration</h4>
                    <p class="text-muted mb-4">Create your admin account and configure site settings.</p>

                    <form method="POST">
                        <div class="mb-3">
                            <label for="admin_name" class="form-label">Admin Full Name</label>
                            <input type="text" class="form-control" id="admin_name" name="admin_name" 
                                   value="<?php echo htmlspecialchars($_POST['admin_name'] ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="admin_email" class="form-label">Admin Email</label>
                            <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                   value="<?php echo htmlspecialchars($_POST['admin_email'] ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="admin_password" class="form-label">Admin Password</label>
                            <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                            <div class="form-text">Minimum 8 characters</div>
                        </div>

                        <div class="mb-3">
                            <label for="site_url" class="form-label">Site URL</label>
                            <input type="url" class="form-control" id="site_url" name="site_url" 
                                   value="<?php echo htmlspecialchars($_POST['site_url'] ?? 'http://localhost/south-safari-v3/'); ?>" required>
                            <div class="form-text">Include trailing slash (e.g., http://yoursite.com/)</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="install.php?step=1" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check me-2"></i>Complete Installation
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
