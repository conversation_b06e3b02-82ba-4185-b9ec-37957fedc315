<?php
/**
 * South Safari Partnership Platform - Configuration File
 * Version 2.0 - Partnership-Centric & Feature-Focused
 * Created: 2025-01-18
 */

// Prevent direct access
if (!defined('SS_INIT')) {
    die('Direct access not permitted');
}

// Define application constants
define('SS_VERSION', '2.0');
define('SS_NAME', 'South Safari Partnership Platform');
define('SS_TAGLINE', 'Partner with SS and Stay Relevant');

// Environment configuration
define('SS_ENVIRONMENT', 'development'); // development, staging, production
define('SS_DEBUG', SS_ENVIRONMENT === 'development');

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'south_safari_partnerships');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Security configuration
define('SS_SECRET_KEY', 'your-secret-key-here-change-in-production');
define('SS_SALT', 'your-salt-here-change-in-production');
define('SESSION_LIFETIME', 3600 * 24 * 7); // 7 days
define('PASSWORD_RESET_EXPIRY', 3600); // 1 hour
define('EMAIL_VERIFICATION_EXPIRY', 3600 * 24); // 24 hours

// File upload configuration
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'zip', 'jpg', 'jpeg', 'png']);

// Email configuration
define('SMTP_HOST', '');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'South Safari Partnership Platform');
define('ADMIN_EMAIL', '<EMAIL>');

// URL configuration
define('BASE_URL', 'http://localhost/south-safari-v3/');
define('ADMIN_URL', BASE_URL . 'admin/');
define('COLLABORATOR_URL', BASE_URL . 'collaborator/');
define('ASSETS_URL', BASE_URL . 'assets/');

// Pagination configuration
define('PARTNERSHIPS_PER_PAGE', 12);
define('MESSAGES_PER_PAGE', 20);
define('CONNECTIONS_PER_PAGE', 15);

// Partnership-specific configuration
define('AUTO_APPROVE_CONNECTIONS', false);
define('REQUIRE_EMAIL_VERIFICATION', true);
define('ENABLE_FILE_UPLOADS', true);
define('ENABLE_MESSAGING', true);

// Feature flags
define('FEATURE_ANALYTICS', true);
define('FEATURE_NOTIFICATIONS', true);
define('FEATURE_API', false);

// Error reporting based on environment
if (SS_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/../logs/error.log');
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/../logs/error.log');
}

// Timezone configuration
date_default_timezone_set('Africa/Johannesburg');

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// Partnership opportunity categories
define('PARTNERSHIP_CATEGORIES', [
    'Food Delivery' => 'Food Delivery',
    'Service Booking' => 'Service Booking', 
    'FinTech' => 'FinTech',
    'Transportation' => 'Transportation',
    'Healthcare' => 'Healthcare',
    'E-Commerce' => 'E-Commerce',
    'Agriculture' => 'Agriculture',
    'Education' => 'Education',
    'Services' => 'Services'
]);

// Partnership models
define('PARTNERSHIP_MODELS', [
    'revenue_share' => 'Revenue Share',
    'equity_partnership' => 'Equity Partnership',
    'profit_sharing' => 'Profit Sharing',
    'hybrid_model' => 'Hybrid Model',
    'service_model' => 'Service Model'
]);

// Connection statuses
define('CONNECTION_STATUSES', [
    'draft' => 'Draft',
    'submitted' => 'Submitted',
    'reviewing' => 'Under Review',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'withdrawn' => 'Withdrawn'
]);

// Partnership statuses
define('PARTNERSHIP_STATUSES', [
    'active' => 'Active',
    'completed' => 'Completed',
    'terminated' => 'Terminated',
    'on_hold' => 'On Hold'
]);

// User roles
define('USER_ROLES', [
    'admin' => 'Administrator',
    'collaborator' => 'Partnership Collaborator'
]);

// User statuses
define('USER_STATUSES', [
    'active' => 'Active',
    'inactive' => 'Inactive',
    'suspended' => 'Suspended'
]);

// Opportunity statuses
define('OPPORTUNITY_STATUSES', [
    'active' => 'Active',
    'inactive' => 'Inactive',
    'filled' => 'Filled',
    'paused' => 'Paused'
]);

// Default feature badges for new opportunities
define('DEFAULT_FEATURE_BADGES', [
    'Online Platform',
    'Mobile Responsive',
    'Secure Payments',
    'User Management',
    'Analytics Dashboard',
    'Customer Support'
]);

// System messages
define('SUCCESS_MESSAGES', [
    'connection_submitted' => 'Your partnership connection has been submitted successfully!',
    'profile_updated' => 'Your profile has been updated successfully!',
    'password_changed' => 'Your password has been changed successfully!',
    'email_verified' => 'Your email has been verified successfully!',
    'partnership_created' => 'Partnership opportunity created successfully!',
    'partnership_updated' => 'Partnership opportunity updated successfully!'
]);

define('ERROR_MESSAGES', [
    'invalid_credentials' => 'Invalid email or password.',
    'email_exists' => 'An account with this email already exists.',
    'email_not_verified' => 'Please verify your email before logging in.',
    'account_suspended' => 'Your account has been suspended. Please contact support.',
    'connection_exists' => 'You have already submitted a connection for this opportunity.',
    'file_too_large' => 'File size exceeds the maximum allowed limit.',
    'invalid_file_type' => 'File type not allowed.',
    'partnership_not_found' => 'Partnership opportunity not found.',
    'access_denied' => 'Access denied. Insufficient permissions.'
]);

// Load environment-specific configuration if exists
$env_config = __DIR__ . '/config.' . SS_ENVIRONMENT . '.php';
if (file_exists($env_config)) {
    require_once $env_config;
}
