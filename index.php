<?php
/**
 * South Safari Partnership Platform - Homepage
 * Version 2.0 - Partnership-Centric & Feature-Focused
 */

// Check if platform is installed
if (!file_exists('.installed')) {
    header('Location: install.php');
    exit;
}

require_once 'includes/init.php';

// Get featured partnership opportunities
$featuredOpportunities = getPartnershipOpportunities(8, true, 'active');

// Get platform statistics
$stats = getPartnershipStats();

$pageTitle = 'Build Meaningful Partnerships That Last';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle($pageTitle); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00B074;
            --secondary-color: #1A1A1A;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--secondary-color);
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .nav-link {
            color: var(--secondary-color) !important;
            font-weight: 500;
            margin: 0 0.5rem;
            transition: color 0.3s;
        }
        
        .nav-link:hover {
            color: var(--primary-color) !important;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, #008c5a 100%);
            color: white;
            padding: 5rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            opacity: 0.9;
        }
        
        .btn-primary {
            background-color: white;
            color: var(--primary-color);
            border: none;
            padding: 1rem 2rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .btn-outline-primary {
            color: white;
            border-color: white;
            padding: 1rem 2rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s;
        }
        
        .btn-outline-primary:hover {
            background-color: white;
            color: var(--primary-color);
            border-color: white;
            transform: translateY(-2px);
        }
        
        .stats-section {
            background: white;
            padding: 3rem 0;
            margin-top: -2rem;
            position: relative;
            z-index: 3;
        }
        
        .stat-card {
            text-align: center;
            padding: 2rem 1rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--secondary-color);
        }
        
        .project-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: all 0.3s;
            height: 100%;
            border: 2px solid transparent;
            display: flex;
            flex-direction: column;
            min-height: 400px;
        }
        
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }
        
        .project-category {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .project-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--secondary-color);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
            min-height: 3.5rem;
        }
        
        .project-description {
            color: #666;
            margin-bottom: 1rem;
            font-size: 0.95rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.5;
            flex-grow: 1;
        }
        
        .feature-list {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .feature-badge {
            background: #E8F5F0;
            color: var(--primary-color);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            border: 1px solid rgba(0, 176, 116, 0.2);
        }
        
        .card-bottom {
            margin-top: auto;
            padding-top: 1rem;
        }
        
        .btn-connect {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-connect:hover {
            background-color: #008c5a;
            color: white;
            transform: translateY(-1px);
        }
        
        .how-it-works {
            background: #f8f9fa;
            padding: 5rem 0;
        }
        
        .step-card {
            text-align: center;
            padding: 2rem 1rem;
        }
        
        .step-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 1.5rem;
        }
        
        .cta-section {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #333 100%);
            color: white;
            padding: 5rem 0;
            text-align: center;
        }
        
        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .cta-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .footer {
            background: var(--secondary-color);
            color: white;
            padding: 3rem 0 1rem;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.2rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-handshake me-2"></i>South Safari
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#opportunities">Partnerships</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#how-it-works">How It Works</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <?php $currentUser = getCurrentUser(); ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i><?php echo escape($currentUser['full_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="<?php echo $currentUser['role'] === 'admin' ? 'admin/' : 'collaborator/'; ?>">
                                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sign Out</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Sign In</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">Join Partnership</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">Build Meaningful Partnerships That Last</h1>
                        <p class="hero-subtitle">Partner with SS and Stay Relevant</p>
                        <p class="mb-4">Connect with Southern Africa's fastest-growing markets through sustainable partnerships. Transform your expertise into recurring revenue streams through collaborative ventures.</p>
                        <div class="d-flex flex-wrap gap-3">
                            <a href="#opportunities" class="btn btn-primary btn-lg">
                                <i class="fas fa-search me-2"></i>Explore Partnerships
                            </a>
                            <a href="register.php" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-handshake me-2"></i>Start Partnership
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-handshake" style="font-size: 15rem; opacity: 0.1;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_opportunities']; ?></div>
                        <div class="stat-label">Partnership Opportunities</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_collaborators']; ?></div>
                        <div class="stat-label">Partners Onboarded</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total_connections']; ?></div>
                        <div class="stat-label">Partnerships Launched</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['active_partnerships']; ?></div>
                        <div class="stat-label">Active Partnerships</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Partnership Opportunities -->
    <section id="opportunities" class="py-5">
        <div class="container">
            <h2 class="section-title">Featured Partnership Opportunities</h2>
            <p class="text-center text-muted mb-5">Discover partnership opportunities that match your expertise and grow your revenue together</p>
            
            <div class="row g-4">
                <?php if (empty($featuredOpportunities)): ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-handshake text-muted" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h4>Partnership Opportunities Coming Soon</h4>
                            <p class="text-muted">We're preparing exciting partnership opportunities for you.</p>
                            <a href="register.php" class="btn btn-primary">Join Our Partnership Community</a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($featuredOpportunities as $opportunity): ?>
                        <div class="col-lg-3 col-md-6">
                            <div class="project-card">
                                <span class="project-category"><?php echo escape($opportunity['category']); ?></span>
                                <h4 class="project-title"><?php echo escape($opportunity['title']); ?></h4>
                                <p class="project-description"><?php echo escape($opportunity['short_description']); ?></p>
                                <div class="feature-list">
                                    <?php 
                                    $features = json_decode($opportunity['product_features'], true);
                                    if ($features && is_array($features)) {
                                        foreach (array_slice($features, 0, 4) as $feature) {
                                            echo "<span class='feature-badge'>" . escape($feature) . "</span>";
                                        }
                                    }
                                    ?>
                                </div>
                                <div class="card-bottom">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">
                                            <?php echo escape($opportunity['revenue_split'] ?: PARTNERSHIP_MODELS[$opportunity['partnership_model']]); ?>
                                        </span>
                                        <a href="partnership.php?slug=<?php echo $opportunity['slug']; ?>" class="btn btn-connect">Connect</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <div class="text-center mt-5">
                <a href="partnerships.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>Explore All Partnerships
                </a>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section id="how-it-works" class="how-it-works">
        <div class="container">
            <h2 class="section-title">How Partnership Works</h2>
            <p class="text-center text-muted mb-5">Simple steps to transform your expertise into sustainable partnership revenue</p>
            
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h4>Discover Partnerships</h4>
                        <p class="text-muted">Explore partnership opportunities that align with your expertise and interests</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <h4>Connect & Collaborate</h4>
                        <p class="text-muted">Connect with us and share your portfolio and proposed approach to the partnership</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h4>Partnership Agreement</h4>
                        <p class="text-muted">Finalize terms and establish partnership agreement for long-term collaboration</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="step-card">
                        <div class="step-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h4>Launch & Prosper</h4>
                        <p class="text-muted">Launch your partnership solution and share recurring revenue from real customers</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <h2 class="cta-title">Ready to Build Partnerships That Matter?</h2>
            <p class="cta-subtitle">Join South Safari today and turn your expertise into sustainable partnership revenue</p>
            <div class="d-flex flex-wrap justify-content-center gap-3">
                <a href="register.php" class="btn btn-light btn-lg">
                    <i class="fas fa-handshake me-2"></i>Start Partnership
                </a>
                <a href="#opportunities" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-search me-2"></i>Browse Opportunities
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5><i class="fas fa-handshake me-2"></i>South Safari</h5>
                    <p class="text-muted">Partner with SS and Stay Relevant. Connecting talented partners with Southern African opportunities.</p>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>Platform</h6>
                    <ul class="list-unstyled">
                        <li><a href="#opportunities" class="text-muted">Partnerships</a></li>
                        <li><a href="#how-it-works" class="text-muted">How It Works</a></li>
                        <li><a href="register.php" class="text-muted">Join Partnership</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>For Partners</h6>
                    <ul class="list-unstyled">
                        <li><a href="login.php" class="text-muted">Sign In</a></li>
                        <li><a href="register.php" class="text-muted">Register</a></li>
                        <li><a href="partnerships.php" class="text-muted">Browse All</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted">Help Center</a></li>
                        <li><a href="#" class="text-muted">Contact Us</a></li>
                        <li><a href="#" class="text-muted">Partnership Guide</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6>Legal</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted">Privacy Policy</a></li>
                        <li><a href="#" class="text-muted">Terms of Service</a></li>
                        <li><a href="#" class="text-muted">Partnership Terms</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; 2025 South Safari Partnership Platform. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-muted me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-muted me-3"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-muted"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <?php displayFlashMessage(); ?>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.backgroundColor = 'white';
                navbar.style.backdropFilter = 'none';
            }
        });
    </script>
</body>
</html>
