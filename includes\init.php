<?php
/**
 * South Safari Partnership Platform - Initialization File
 * Version 2.0 - Partnership-Centric & Feature-Focused
 * Created: 2025-01-18
 */

// Define initialization constant
define('SS_INIT', true);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load configuration
require_once __DIR__ . '/config.php';

// Load core classes and functions
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/functions.php';

// Set security headers
setSecurityHeaders();

// Initialize database connection
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    if (SS_DEBUG) {
        die('Database initialization failed: ' . $e->getMessage());
    } else {
        die('System temporarily unavailable. Please try again later.');
    }
}

// Initialize authentication
$auth = Auth::getInstance();

// Set timezone
date_default_timezone_set('Africa/Johannesburg');

// Global error handler
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorMessage = "Error: {$message} in {$file} on line {$line}";
    error_log($errorMessage);
    
    if (SS_DEBUG) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 10px; margin: 10px; border-left: 4px solid #c62828;'>";
        echo "<strong>Debug Error:</strong> " . htmlspecialchars($errorMessage);
        echo "</div>";
    }
    
    return true;
});

// Global exception handler
set_exception_handler(function($exception) {
    $errorMessage = "Uncaught exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();
    error_log($errorMessage);
    
    if (SS_DEBUG) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 10px; margin: 10px; border-left: 4px solid #c62828;'>";
        echo "<strong>Debug Exception:</strong> " . htmlspecialchars($errorMessage);
        echo "<pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
        echo "</div>";
    } else {
        http_response_code(500);
        echo "An error occurred. Please try again later.";
    }
});

// Create necessary directories
$directories = [
    __DIR__ . '/../uploads',
    __DIR__ . '/../uploads/partnerships',
    __DIR__ . '/../uploads/connections',
    __DIR__ . '/../uploads/profiles',
    __DIR__ . '/../logs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        
        // Create .htaccess to protect upload directories
        if (strpos($dir, 'uploads') !== false) {
            file_put_contents($dir . '/.htaccess', "deny from all\n");
        }
    }
}

// Load system settings into constants (if not already defined)
try {
    $settings = $db->fetchAll("SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_public = 1");
    foreach ($settings as $setting) {
        $constantName = 'SETTING_' . strtoupper($setting['setting_key']);
        if (!defined($constantName)) {
            $value = $setting['setting_value'];
            
            // Convert based on type
            switch ($setting['setting_type']) {
                case 'boolean':
                    $value = (bool) $value;
                    break;
                case 'integer':
                    $value = (int) $value;
                    break;
                case 'json':
                    $value = json_decode($value, true);
                    break;
            }
            
            define($constantName, $value);
        }
    }
} catch (Exception $e) {
    // Settings table might not exist yet during installation
    if (SS_DEBUG) {
        error_log("Could not load system settings: " . $e->getMessage());
    }
}

// Helper function to include template parts
function includeTemplate($template, $variables = []) {
    extract($variables);
    $templatePath = __DIR__ . '/../templates/' . $template . '.php';
    
    if (file_exists($templatePath)) {
        include $templatePath;
    } else {
        if (SS_DEBUG) {
            echo "Template not found: {$template}";
        }
    }
}

// Helper function to get page title
function getPageTitle($title = '') {
    $siteTitle = defined('SETTING_SITE_NAME') ? SETTING_SITE_NAME : SS_NAME;
    $tagline = defined('SETTING_SITE_TAGLINE') ? SETTING_SITE_TAGLINE : SS_TAGLINE;
    
    if ($title) {
        return $title . ' - ' . $siteTitle;
    }
    
    return $siteTitle . ' - ' . $tagline;
}

// Helper function to check if we're on a specific page
function isCurrentPage($page) {
    $currentScript = basename($_SERVER['SCRIPT_NAME'], '.php');
    return $currentScript === $page;
}

// Helper function to get navigation class
function getNavClass($page) {
    return isCurrentPage($page) ? 'nav-link active' : 'nav-link';
}

// Helper function to load partnership opportunities
function getPartnershipOpportunities($limit = null, $featured = null, $status = 'active') {
    $sql = "SELECT * FROM partnership_opportunities WHERE status = ?";
    $params = [$status];
    
    if ($featured !== null) {
        $sql .= " AND featured = ?";
        $params[] = $featured ? 1 : 0;
    }
    
    $sql .= " ORDER BY display_order ASC, created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = $limit;
    }
    
    return db()->fetchAll($sql, $params);
}

// Helper function to get partnership opportunity by slug
function getPartnershipBySlug($slug) {
    return db()->fetchRow(
        "SELECT * FROM partnership_opportunities WHERE slug = ? AND status = 'active'",
        [$slug]
    );
}

// Helper function to check if user has connection to opportunity
function hasConnectionToOpportunity($opportunityId, $userId = null) {
    if (!$userId) {
        $user = getCurrentUser();
        $userId = $user ? $user['id'] : null;
    }
    
    if (!$userId) return false;
    
    return db()->exists(
        'partnership_connections',
        'opportunity_id = ? AND collaborator_id = ?',
        [$opportunityId, $userId]
    );
}

// Helper function to get user's connections
function getUserConnections($userId, $status = null) {
    $sql = "SELECT pc.*, po.title, po.slug, po.category 
            FROM partnership_connections pc 
            JOIN partnership_opportunities po ON pc.opportunity_id = po.id 
            WHERE pc.collaborator_id = ?";
    $params = [$userId];
    
    if ($status) {
        $sql .= " AND pc.status = ?";
        $params[] = $status;
    }
    
    $sql .= " ORDER BY pc.created_at DESC";
    
    return db()->fetchAll($sql, $params);
}

// Helper function to get partnership statistics
function getPartnershipStats() {
    return [
        'total_opportunities' => db()->count('partnership_opportunities', 'status = ?', ['active']),
        'total_collaborators' => db()->count('users', 'role = ? AND status = ?', ['collaborator', 'active']),
        'total_connections' => db()->count('partnership_connections', 'status IN (?, ?)', ['submitted', 'approved']),
        'active_partnerships' => db()->count('active_partnerships', 'status = ?', ['active'])
    ];
}

// Initialize any startup tasks
if (function_exists('runStartupTasks')) {
    runStartupTasks();
}

// Log successful initialization in debug mode
if (SS_DEBUG) {
    error_log("South Safari Partnership Platform initialized successfully");
}
