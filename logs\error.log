[18-Jul-2025 14:52:28 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_public = 1
[18-Jul-2025 14:52:28 Africa/Johannesburg] Could not load system settings: Database query failed
[18-Jul-2025 14:52:28 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 14:52:28 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.partnership_opportunities' doesn't exist SQL: SELECT * FROM partnership_opportunities WHERE status = ? ORDER BY display_order ASC, created_at DESC LIMIT ?
[18-Jul-2025 14:52:28 Africa/Johannesburg] Error fetching partnership opportunities: Database query failed
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  session_start(): Session cannot be started after headers have already been sent in C:\xampp\htdocs\south-safari-v3\includes\init.php on line 16
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\check-system.php:10) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 365
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\check-system.php:10) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 368
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\check-system.php:10) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 371
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\check-system.php:10) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 374
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\check-system.php:10) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 377
[18-Jul-2025 14:53:29 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  session_start(): Session cannot be started after headers have already been sent in C:\xampp\htdocs\south-safari-v3\includes\auth.php on line 36
[18-Jul-2025 14:53:29 Africa/Johannesburg] PHP Warning:  session_regenerate_id(): Session ID cannot be regenerated when there is no active session in C:\xampp\htdocs\south-safari-v3\includes\auth.php on line 51
[18-Jul-2025 14:53:29 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_public = 1
[18-Jul-2025 14:53:29 Africa/Johannesburg] Could not load system settings: Database query failed
[18-Jul-2025 14:53:29 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 14:53:29 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.partnership_opportunities' doesn't exist SQL: SELECT * FROM partnership_opportunities WHERE status = ? ORDER BY display_order ASC, created_at DESC LIMIT ?
[18-Jul-2025 14:53:29 Africa/Johannesburg] Error fetching partnership opportunities: Database query failed
[18-Jul-2025 14:54:43 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 14:54:43 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_public = 1
[18-Jul-2025 14:54:43 Africa/Johannesburg] Could not load system settings: Database query failed
[18-Jul-2025 14:54:43 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 14:54:43 Africa/Johannesburg] Error: Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\south-safari-v3\register.php on line 19
[18-Jul-2025 14:55:07 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 14:55:07 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_public = 1
[18-Jul-2025 14:55:07 Africa/Johannesburg] Could not load system settings: Database query failed
[18-Jul-2025 14:55:07 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 14:55:07 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.partnership_opportunities' doesn't exist SQL: SELECT COUNT(*) as count FROM partnership_opportunities WHERE status = 'active'
[18-Jul-2025 14:55:07 Africa/Johannesburg] Uncaught exception: Database query failed in C:\xampp\htdocs\south-safari-v3\includes\database.php on line 78
[18-Jul-2025 14:55:16 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 14:55:16 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_public = 1
[18-Jul-2025 14:55:16 Africa/Johannesburg] Could not load system settings: Database query failed
[18-Jul-2025 14:55:16 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 14:55:16 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.partnership_opportunities' doesn't exist SQL: SELECT * FROM partnership_opportunities WHERE status = ? ORDER BY display_order ASC, created_at DESC LIMIT ?
[18-Jul-2025 14:55:16 Africa/Johannesburg] Error fetching partnership opportunities: Database query failed
[18-Jul-2025 14:55:36 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 14:55:36 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.system_settings' doesn't exist SQL: SELECT setting_key, setting_value, setting_type FROM system_settings WHERE is_public = 1
[18-Jul-2025 14:55:36 Africa/Johannesburg] Could not load system settings: Database query failed
[18-Jul-2025 14:55:36 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 14:55:36 Africa/Johannesburg] Database query error: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'south_safari.partnership_opportunities' doesn't exist SQL: SELECT * FROM partnership_opportunities WHERE status = ? ORDER BY display_order ASC, created_at DESC LIMIT ?
[18-Jul-2025 14:55:36 Africa/Johannesburg] Error fetching partnership opportunities: Database query failed
[18-Jul-2025 14:59:40 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 14:59:40 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:00:11 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:00:11 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:00:11 Africa/Johannesburg] Error: Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\south-safari-v3\register.php on line 19
[18-Jul-2025 15:00:46 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:00:46 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:00:46 Africa/Johannesburg] Error: Undefined array key "REQUEST_METHOD" in C:\xampp\htdocs\south-safari-v3\login.php on line 18
[18-Jul-2025 15:01:40 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:01:40 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:02:51 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:02:51 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:03:07 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:03:07 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:03:33 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:03:33 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  session_start(): Session cannot be started after headers have already been sent in C:\xampp\htdocs\south-safari-v3\includes\init.php on line 16
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 365
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 368
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 371
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 374
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 377
[18-Jul-2025 15:05:11 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  session_start(): Session cannot be started after headers have already been sent in C:\xampp\htdocs\south-safari-v3\includes\auth.php on line 36
[18-Jul-2025 15:05:11 Africa/Johannesburg] PHP Warning:  session_regenerate_id(): Session ID cannot be regenerated when there is no active session in C:\xampp\htdocs\south-safari-v3\includes\auth.php on line 51
[18-Jul-2025 15:05:11 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:05:11 Africa/Johannesburg] Error: Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\index.php on line 9
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  session_start(): Session cannot be started after headers have already been sent in C:\xampp\htdocs\south-safari-v3\includes\init.php on line 16
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 365
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 368
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 371
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 374
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  Cannot modify header information - headers already sent by (output started at C:\xampp\htdocs\south-safari-v3\comprehensive-test.php:9) in C:\xampp\htdocs\south-safari-v3\includes\security.php on line 377
[18-Jul-2025 15:05:38 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  session_start(): Session cannot be started after headers have already been sent in C:\xampp\htdocs\south-safari-v3\includes\auth.php on line 36
[18-Jul-2025 15:05:38 Africa/Johannesburg] PHP Warning:  session_regenerate_id(): Session ID cannot be regenerated when there is no active session in C:\xampp\htdocs\south-safari-v3\includes\auth.php on line 51
[18-Jul-2025 15:05:38 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:05:57 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:05:57 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:06:03 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:06:03 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:06:03 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:06:03 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:06:10 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:06:10 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:06:44 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:06:44 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:08:07 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:08:07 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
[18-Jul-2025 15:09:50 Africa/Johannesburg] Database connected successfully
[18-Jul-2025 15:09:50 Africa/Johannesburg] South Safari Partnership Platform initialized successfully
